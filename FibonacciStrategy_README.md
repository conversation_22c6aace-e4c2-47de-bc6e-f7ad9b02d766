# Fibonacci Strategy

## Overview
This NinjaTrader strategy implements a Fibonacci-based trading system that buys at the 1.0 level (green line) and sells at the 0.0 level (red line) based on the previous candle's Fibonacci levels. The strategy is a direct 1:1 conversion of the TradingView Pine Script logic.

## How It Works

### Signal Logic
The strategy follows the exact logic from the TradingView script:

1. **Calculate Fibonacci Levels**: For each candle (or MTF period), calculate the 1.0 level (100% - green line) and 0.0 level (0% - red line) based on the high/low range
2. **Use Previous Levels**: The strategy looks at the PREVIOUS candle's Fibonacci levels for signal generation
3. **Buy Signal**: When price touches the previous candle's 1.0 level (green line)
4. **Sell Signal**: When price touches the previous candle's 0.0 level (red line)

### Example
```
9:30 candle: High=100, Low=90
- Green line (1.0) = 90 + (10 * 1.0) = 100
- Red line (0.0) = 90 + (10 * 0.0) = 90

9:45 candle: If price touches 100 (the 9:30 green line) → BUY signal
9:45 candle: If price touches 90 (the 9:30 red line) → SELL signal
```

### Position Management
- **Take Profit**: Set at 1.1 level (110% of the range)
- **Stop Loss**: Set at -0.1 level (-10% of the range)
- **Entry Orders**: Market orders when signal conditions are met
- **Position Tracking**: Prevents multiple entries in same direction

## Strategy Parameters

### Multi-Timeframe Settings
- **Use Multi-Timeframe Mode**: Enable MTF Fibonacci strategy (default: false)
- **MTF Timeframe Value**: Multi-timeframe period value (default: 5)
- **MTF Period Type**: Multi-timeframe period type (default: Minute)
- **Use Confirmed Data**: Use confirmed MTF data to prevent repainting (default: true)

### Signal Settings
- **Show Signals**: Enable signal generation (default: true)
- **Show Immediate Signals**: Allow signals even with active positions (default: true)

### Risk Management
- **Take Profit Multiplier**: TP level multiplier - 1.1 = 110% level (default: 1.1)
- **Stop Loss Multiplier**: SL level multiplier - -0.1 = -10% level (default: -0.1)

## Trading Modes

### Per-Bar Mode (Default)
- Uses the previous bar's high/low to calculate Fibonacci levels
- Generates signals when current bar touches previous bar's levels
- Suitable for shorter timeframes and more frequent signals

### Multi-Timeframe Mode
- Uses a higher timeframe (e.g., 5-minute) to calculate Fibonacci levels
- Applies those levels to the chart timeframe for signal generation
- Provides more stable signals with less noise
- Example: 1-minute chart using 5-minute Fibonacci levels

## Signal Conditions

### Buy Signal
```csharp
bool buyCondition = High[0] >= previousGreenLevel && 
                   Low[0] <= previousGreenLevel && 
                   !greenSignalTriggered;
```

### Sell Signal
```csharp
bool sellCondition = Low[0] <= previousRedLevel && 
                    High[0] >= previousRedLevel && 
                    !redSignalTriggered;
```

## Risk Management Features

1. **Automatic Stop Loss**: Set at -0.1 level (below the red line for longs, above green line for shorts)
2. **Automatic Take Profit**: Set at 1.1 level (extended beyond the range)
3. **Position Tracking**: Prevents overlapping positions
4. **Signal Flags**: Prevents multiple signals per candle/MTF period
5. **Order Management**: Handles order fills, cancellations, and rejections

## Usage Instructions

1. **Add to Chart**: Apply the FibonacciStrategy to your chart
2. **Configure Parameters**: Set your preferred timeframe and risk parameters
3. **Enable Strategy**: Make sure "Show Signals" is enabled
4. **Monitor**: Watch for BUY/SELL signals in the output window
5. **Backtest**: Use Strategy Analyzer to test historical performance

## Key Features

- **Exact TradingView Logic**: 1:1 conversion of the Pine Script signal logic
- **Multi-Timeframe Support**: Can use higher timeframe Fibonacci levels
- **Robust Position Management**: Handles all order states and position tracking
- **Configurable Risk**: Adjustable TP/SL multipliers
- **No Repainting**: Uses confirmed data when enabled
- **Comprehensive Logging**: Detailed output for debugging and monitoring

## Notes

- The strategy uses `Calculate.OnPriceChange` to execute on every price change, allowing real-time detection of Fibonacci level touches
- This provides faster signal execution compared to `Calculate.OnBarClose` but may generate more frequent signals
- Position sizes are handled by NinjaTrader's default settings
- The strategy will automatically close positions at session end if enabled
- All price levels are calculated dynamically based on the previous candle's range

## Execution Modes

The strategy now executes on **tick data** using `Calculate.OnPriceChange`:

### Real-Time Execution
- **Immediate signals**: Signals are generated immediately when price touches Fibonacci levels
- **Current price detection**: Uses `Close[0]`, `GetCurrentBid()`, and `GetCurrentAsk()` for precise tick-level execution
- **Price tolerance**: Uses a tolerance of 1 tick size to account for spread and minor price variations
- **No waiting**: No need to wait for candle close to detect level touches

### Historical Backtesting
- **Consistent tick logic**: Uses current price with tolerance for both historical and real-time execution
- **True tick-based entries**: Buys and sells based on current tick price, not bar OHLC ranges
- **Standard resolution fills**: Uses `OrderFillResolution.Standard` (compatible with multi-timeframe mode)
- **Tick replay support**: For most accurate results, enable tick replay in Strategy Analyzer

### Performance Considerations
- **Higher CPU usage**: More frequent calculations compared to OnBarClose mode
- **More responsive**: Catches brief level touches that OnBarClose might miss
- **Better fills**: More realistic entry and exit prices in both live and historical testing
