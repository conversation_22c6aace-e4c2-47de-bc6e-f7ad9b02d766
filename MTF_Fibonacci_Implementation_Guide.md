# Multi-Timeframe Fibonacci Indicator Implementation Guide

## Overview
The FibonacciPerBarIndicator has been enhanced with multi-timeframe (MTF) functionality that works similarly to TradingView indicators. The indicator can now operate in two modes:

1. **Per-Bar Mode** (Original): Draws Fibonacci levels based on each individual bar's high/low
2. **Multi-Timeframe Mode** (New): Draws Fibonacci levels based on higher timeframe candle OHLC data

## Key Features Added

### Multi-Timeframe Functionality
- Uses NinjaScript's `AddDataSeries()` to add higher timeframe data
- <PERSON>perly handles `BarsInProgress` to process only primary timeframe events
- Detects new MTF periods using time comparison
- Supports both confirmed (no repaint) and real-time (repainting) modes

### MTF Settings
- **Use Multi-Timeframe Mode**: Enable/disable MTF functionality
- **MTF Timeframe Value**: The period value (e.g., 5 for 5-minute)
- **MTF Period Type**: The timeframe type (Minute, Hour, Day, etc.)
- **Use Confirmed Data**: Choose between no-repaint vs real-time modes

## Technical Implementation

### State Management
```csharp
// State.Configure: Add MTF data series
if (UseMtfMode)
{
    AddDataSeries(MtfPeriodType, MtfTimeframe);
}

// State.DataLoaded: Initialize arrays and variables
```

### MTF Bar Detection
```csharp
// Detect new MTF bar using time comparison
DateTime currentMtfTime = Times[1][0];
isNewMtfBar = currentMtfTime != lastMtfBarTime;
```

### Data Access Patterns
- **Confirmed Mode**: Uses `[1]` offset for previous completed MTF bar
- **Real-time Mode**: Uses `[0]` offset for current developing MTF bar
- **BarsInProgress**: Only processes primary timeframe (index 0) for drawing

### Line Drawing Logic
- **Per-Bar Mode**: Lines span single bar (0 to 1)
- **MTF Mode**: Lines span calculated MTF duration in chart bars
- **Unique Naming**: Uses MTF timestamp for unique line identification

## Usage Instructions

### Basic Setup
1. Add the indicator to your chart
2. Enable "Use Multi-Timeframe Mode"
3. Set your desired MTF timeframe (e.g., 5 minutes on a 1-minute chart)
4. Choose confirmed vs real-time data mode

### Recommended Settings
- **For Backtesting**: Use Confirmed Data = true (no repaint)
- **For Live Trading**: Choose based on preference (confirmed = delayed, real-time = immediate)
- **MTF Timeframe**: Should be higher than chart timeframe (e.g., 5M MTF on 1M chart)

### Behavior Comparison to TradingView
- Lines are limited to individual MTF timeframe periods (not infinite)
- New Fibonacci levels are drawn when new MTF period begins
- Previous levels are replaced with new calculations
- Matches TradingView MTF indicator behavior patterns

## Code Structure

### Key Methods
- `OnBarUpdate()`: Main logic with MTF vs per-bar mode handling
- `DrawFibonacciLevels()`: Unified drawing method for both modes
- `CalculateMtfDurationInBars()`: Calculates line span duration
- `GetBarDuration()`: Helper for timeframe calculations

### Multi-Timeframe Variables
- `lastMtfBarTime`: Tracks MTF bar changes
- `isNewMtfBar`: Flags new MTF period detection
- `mtfHigh/Low/Open/Close`: Stores MTF OHLC data

## Error Handling
- Validates sufficient bars on both timeframes
- Handles missing MTF data gracefully
- Prevents drawing with zero price ranges
- Uses fallback values for duration calculations

## Performance Considerations
- Only processes primary timeframe events for drawing
- Efficient MTF bar detection using time comparison
- Minimal memory footprint with reused variables
- Optimized line naming for proper cleanup

## Testing
A test strategy (`TestFibonacciMTF.cs`) is included to verify:
- Compilation without errors
- Basic functionality in both modes
- Property initialization
- MTF mode activation

This implementation provides professional-grade multi-timeframe functionality that integrates seamlessly with NinjaTrader's architecture while maintaining the familiar behavior patterns from TradingView indicators.
