//
// Test file to verify FibonacciPerBarIndicator compilation
//
#region Using declarations
using System;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Windows.Media;
using NinjaTrader.Cbi;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.NinjaScript.Indicators;
#endregion

namespace NinjaTrader.NinjaScript.Strategies
{
	public class TestFibonacciMTF : Strategy
	{
		private FibonacciPerBarIndicator fibIndicator;

		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description = @"Test strategy for FibonacciPerBarIndicator with MTF";
				Name = "TestFibonacciMTF";
				Calculate = Calculate.OnBarClose;
				EntriesPerDirection = 1;
				EntryHandling = EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy = true;
				ExitOnSessionCloseSeconds = 30;
				IsFillLimitOnTouch = false;
				MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution = OrderFillResolution.Standard;
				Slippage = 0;
				StartBehavior = StartBehavior.WaitUntilFlat;
				TimeInForce = TimeInForce.Gtc;
				TraceOrders = false;
				RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling = StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade = 20;
				IsInstantiatedOnEachOptimizationIteration = true;
			}
			else if (State == State.DataLoaded)
			{
				// Test both modes
				fibIndicator = FibonacciPerBarIndicator();
				
				// Test MTF mode
				fibIndicator.UseMtfMode = true;
				fibIndicator.MtfTimeframe = 5;
				fibIndicator.MtfPeriodType = BarsPeriodType.Minute;
				fibIndicator.UseConfirmedData = true;
				fibIndicator.ShowLevels = true;
				fibIndicator.ShowLabels = true;
			}
		}

		protected override void OnBarUpdate()
		{
			// Simple test - just verify the indicator doesn't crash
			if (CurrentBar < BarsRequiredToTrade)
				return;
				
			// The indicator should be drawing Fibonacci levels automatically
			// This is just a test to ensure it compiles and runs
		}
	}
}
